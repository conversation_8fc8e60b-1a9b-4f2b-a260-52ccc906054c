from typing import Optional, Dict, Any, List
from datetime import datetime

from app.models import Ai<PERSON><PERSON><PERSON><PERSON>
from app.utils.enum import Chat<PERSON>tatus
from app.models.user import User
from app.core.logging import get_logger
from app.api.schemas.ai_chat import ChatMessageRequest

logger = get_logger(__name__)


class AiChatLogService:
    """AI对话日志服务"""

    @staticmethod
    async def create_chat_log(
        user: User,
        chat_request: ChatMessageRequest,
        user_identifier: str,
        moderation_passed: bool = True,
        moderation_message: Optional[str] = None
    ) -> AiChatLog:
        """
        创建AI对话日志记录
        """
        try:
            # 创建日志记录
            chat_log = await AiChatLog.create(
                user_id=user.id,
                user_identifier=user_identifier,
                conversation_id=chat_request.conversation_id,
                query=chat_request.query,
                status=ChatStatus.PENDING,
                moderation_passed=moderation_passed,
                moderation_message=moderation_message,
            )

            logger.info(f"创建AI对话日志记录: {chat_log.id}, 用户: {user.username}")
            return chat_log
        except Exception as e:
            logger.error(f"创建AI对话日志失败: {str(e)}")
            raise

    @staticmethod
    async def update_chat_log_processing(
        chat_log: AiChatLog,
        time_to_first_token: int,
        conversation_id: str,
        message_id: str
    ) -> None:
        """
        更新日志状态为处理中
        """
        try:
            update_data = {
                "status": ChatStatus.PROCESSING,
                "time_to_first_token": time_to_first_token,
                "conversation_id": conversation_id,
                "message_id": message_id
            }

            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.info(f"更新日志状态为处理中: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志处理状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_success(
        chat_log: AiChatLog,
        response_time_ms:int
    ) -> None:
        """
        更新日志状态为成功
        """
        try:
            update_data = {"status": ChatStatus.SUCCESS, "response_time_ms": response_time_ms}
            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.info(f"更新日志状态为成功: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志成功状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_error(
        chat_log: AiChatLog,
        error_message: str,
        response_time_ms: int
    ) -> None:
        """
        更新日志状态为错误
        """
        try:
            update_data = {"status": ChatStatus.ERROR,
                           "error_message": error_message,
                           "response_time_ms": response_time_ms}

            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.warning(f"更新日志状态为错误: {chat_log.id}, 错误: {error_message}")
        except Exception as e:
            logger.error(f"更新日志错误状态失败: {str(e)}")

    @staticmethod
    async def get_conversation_message_count(
        user_id: str,
        conversation_id: str
    ) -> int:
        """
        统计某个用户某个conversation下的数据条数
        """
        try:
            count = await AiChatLog.filter(
                user_id=user_id,
                conversation_id=conversation_id,
                status=ChatStatus.SUCCESS
            ).count()
            return count

        except Exception as e:
            logger.error(f"统计用户会话消息数量失败: {str(e)}")
            return 0

# 全局服务实例
ai_chat_log_service = AiChatLogService()
