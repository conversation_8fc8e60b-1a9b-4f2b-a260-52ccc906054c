from typing import Optional, Dict, Any, List
from datetime import datetime

from app.models import Ai<PERSON><PERSON><PERSON><PERSON>
from app.utils.enum import Chat<PERSON>tatus
from app.models.user import User
from app.core.logging import get_logger
from app.api.schemas.ai_chat import ChatMessageRequest

logger = get_logger(__name__)


class AiChatLogService:
    """AI对话日志服务"""

    @staticmethod
    async def create_chat_log(
        user: User,
        chat_request: ChatMessageRequest,
        user_identifier: str,
        moderation_passed: bool = True,
        moderation_message: Optional[str] = None
    ) -> AiChatLog:
        """
        创建AI对话日志记录
        """
        try:
            # 创建日志记录
            chat_log = await AiChatLog.create(
                user_id=user.id,
                user_identifier=user_identifier,
                conversation_id=chat_request.conversation_id,
                query=chat_request.query,
                status=ChatStatus.PENDING,
                moderation_passed=moderation_passed,
                moderation_message=moderation_message,
            )

            logger.info(f"创建AI对话日志记录: {chat_log.id}, 用户: {user.username}")
            return chat_log
        except Exception as e:
            logger.error(f"创建AI对话日志失败: {str(e)}")
            raise

    @staticmethod
    async def update_chat_log_processing(
        chat_log: AiChatLog,
        time_to_first_token: int,
        conversation_id: Optional[str] = None,
        message_id: Optional[str] = None
    ) -> None:
        """
        更新日志状态为处理中
        """
        try:
            update_data = {"status": ChatStatus.PROCESSING}
            update_data["time_to_first_token"] = time_to_first_token
            if conversation_id:
                update_data["conversation_id"] = conversation_id
            if message_id:
                update_data["message_id"] = message_id

            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.debug(f"更新日志状态为处理中: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志处理状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_success(
        chat_log: AiChatLog,
        response_time_ms:int
    ) -> None:
        """
        更新日志状态为成功
        """
        try:
            update_data = {"status": ChatStatus.SUCCESS}
            if response_time_ms:
                update_data["response_time_ms"] = response_time_ms

            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.info(f"更新日志状态为成功: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志成功状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_error(
        chat_log: AiChatLog,
        error_message: str,
        response_time_ms: int
    ) -> None:
        """
        更新日志状态为错误
        
        Args:
            chat_log: 日志记录
            error_message: 错误信息
            status: 错误状态类型
            response_time_ms: 响应时间
        """
        try:
            update_data = {"status": ChatStatus.ERROR,
                           "error_message": error_message,
                           "response_time_ms": response_time_ms}

            await chat_log.update_from_dict(update_data)
            await chat_log.save()
            logger.warning(f"更新日志状态为错误: {chat_log.id}, 错误: {error_message}")

        except Exception as e:
            logger.error(f"更新日志错误状态失败: {str(e)}")


    @staticmethod
    async def get_user_chat_logs(
        user_id: str,
        limit: int = 20,
        offset: int = 0,
        conversation_id: Optional[str] = None,
        status: Optional[ChatStatus] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[AiChatLog]:
        """
        获取用户的对话日志
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            conversation_id: 会话ID过滤
            status: 状态过滤
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            日志记录列表
        """
        try:
            query = AiChatLog.filter(user_id=user_id)
            
            if conversation_id:
                query = query.filter(conversation_id=conversation_id)
            if status:
                query = query.filter(status=status)
            if start_time:
                query = query.filter(created_at__gte=start_time)
            if end_time:
                query = query.filter(created_at__lte=end_time)

            logs = await query.order_by("-created_at").offset(offset).limit(limit)
            return logs

        except Exception as e:
            logger.error(f"获取用户对话日志失败: {str(e)}")
            return []

    @staticmethod
    async def get_chat_statistics(
        user_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取对话统计信息
        
        Args:
            user_id: 用户ID（可选）
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            统计信息字典
        """
        try:
            query = AiChatLog.all()
            
            if user_id:
                query = query.filter(user_id=user_id)
            if start_time:
                query = query.filter(created_at__gte=start_time)
            if end_time:
                query = query.filter(created_at__lte=end_time)

            total_count = await query.count()
            success_count = await query.filter(status=ChatStatus.SUCCESS).count()
            error_count = await query.filter(status=ChatStatus.ERROR).count()
            
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            return {
                "total_count": total_count,
                "success_count": success_count,
                "error_count": error_count,
                "success_rate": round(success_rate, 2),
                "period": {
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None
                }
            }

        except Exception as e:
            logger.error(f"获取对话统计信息失败: {str(e)}")
            return {}

# 全局服务实例
ai_chat_log_service = AiChatLogService()
